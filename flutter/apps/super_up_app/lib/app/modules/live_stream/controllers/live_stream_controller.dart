// Copyright 2023, the hate<PERSON>ragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';
import 'package:v_platform/v_platform.dart';

// Conditional import for Agora
import 'package:agora_rtc_engine/agora_rtc_engine.dart'
    if (dart.library.html) '../stubs/agora_stubs.dart';

import '../models/live_stream_model.dart';
import '../models/live_stream_participant_model.dart' as participant_model;
import '../models/stream_filter_model.dart';
import '../services/live_stream_api_service.dart';
import 'watch_live_controller.dart';
import 'stream_filter_controller.dart';

class LiveStreamController extends ChangeNotifier {
  final LiveStreamApiService _apiService = GetIt.I.get<LiveStreamApiService>();

  RtcEngine? agoraEngine;
  LiveStreamModel? currentStream;
  bool isStreamer = false;

  final ValueNotifier<bool> isLoading = ValueNotifier(false);
  final ValueNotifier<String> loadingStatus = ValueNotifier('Connecting...');
  final ValueNotifier<bool> isMuted = ValueNotifier(false);
  final ValueNotifier<bool> isCameraOn = ValueNotifier(true);
  final ValueNotifier<bool> isSpeakerOn = ValueNotifier(false);
  final ValueNotifier<List<int>> remoteUsers = ValueNotifier([]);
  final ValueNotifier<int> viewerCount = ValueNotifier(0);
  final ValueNotifier<bool> streamEnded = ValueNotifier(false);
  final ValueNotifier<int> likesCount = ValueNotifier(0);
  final ValueNotifier<bool> isLiked = ValueNotifier(false);

  StreamSubscription? _socketSubscription;
  Timer? _viewerCountTimer;
  Timer? _qualityUpgradeTimer;
  bool _isDisposed = false;

  // Filter controller
  final StreamFilterController _filterController = StreamFilterController();

  // Callback for when stream ends (for participants)
  VoidCallback? onStreamEndedCallback;
  Function(String?, bool)? onStreamEndedWithReasonCallback;

  // Getters
  StreamFilterController get filterController => _filterController;

  Future<void> initializeStream({
    required LiveStreamModel stream,
    required bool isStreamer,
  }) async {
    // Reset disposal flag when starting a new stream
    _isDisposed = false;

    currentStream = stream;
    this.isStreamer = isStreamer;

    // Check if running on web - live streaming not supported
    if (VPlatforms.isWeb) {
      if (kDebugMode) {
        print('Live streaming not supported on web platform');
      }
      isLoading.value = false;
      return;
    }

    if (!_isDisposed) {
      isLoading.value = true;
      loadingStatus.value = 'Requesting permissions...';
    }

    try {
      // Request permissions
      await _requestPermissions();

      // Pre-warm camera for streamers to reduce loading time
      if (isStreamer) {
        if (!_isDisposed) {
          loadingStatus.value = 'Preparing camera...';
        }
        await _preWarmCamera();
      }

      // Initialize Agora engine
      if (!_isDisposed) {
        loadingStatus.value = 'Initializing video engine...';
      }
      await _initializeAgoraEngine();

      // Ensure engine is initialized before proceeding
      if (agoraEngine == null) {
        throw Exception('Failed to initialize Agora engine');
      }

      // Join the stream
      if (!_isDisposed) {
        loadingStatus.value = 'Joining stream...';
      }
      if (isStreamer) {
        await _joinAsStreamer();
      } else {
        await _joinAsViewer();
      }

      // Listen to socket events
      _listenToSocketEvents();

      // Set up filter broadcasting for hosts
      if (isStreamer) {
        _setupFilterBroadcasting();
      }

      // Load join requests for streamers
      if (isStreamer) {
        await loadJoinRequests();
      }

      // Final status update
      if (!_isDisposed) {
        loadingStatus.value = 'Connected!';
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing stream: $e');
      }
      // Ensure loading is set to false even on error
      if (!_isDisposed) {
        isLoading.value = false;
      }
      rethrow;
    } finally {
      // Only set loading to false if no error occurred
      if (agoraEngine != null && !_isDisposed) {
        isLoading.value = false;
      }
    }
  }

  Future<void> _requestPermissions() async {
    if (isStreamer) {
      // Streamer needs camera and microphone permissions
      await [
        Permission.camera,
        Permission.microphone,
      ].request();
    } else {
      // Viewer only needs microphone permission for potential interaction
      await Permission.microphone.request();
    }
  }

  Future<void> _initializeAgoraEngine() async {
    // Create Agora engine
    agoraEngine = createAgoraRtcEngine();

    await agoraEngine!.initialize(RtcEngineContext(
      appId: SConstants.agoraAppId,
      channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
    ));

    // Set event handlers
    agoraEngine!.registerEventHandler(RtcEngineEventHandler(
      onJoinChannelSuccess: (RtcConnection connection, int elapsed) {
        if (kDebugMode) {
          print('Successfully joined channel: ${connection.channelId}');
        }
      },
      onUserJoined: (RtcConnection connection, int remoteUid, int elapsed) {
        if (kDebugMode) {
          print('Remote user joined: $remoteUid');
        }
        if (!_isDisposed) {
          final users = List<int>.from(remoteUsers.value);
          users.add(remoteUid);
          remoteUsers.value = users;
        }
      },
      onUserOffline: (RtcConnection connection, int remoteUid,
          UserOfflineReasonType reason) {
        if (kDebugMode) {
          print('Remote user left: $remoteUid');
        }
        if (!_isDisposed) {
          final users = List<int>.from(remoteUsers.value);
          users.remove(remoteUid);
          remoteUsers.value = users;
        }
      },
      onLeaveChannel: (RtcConnection connection, RtcStats stats) {
        if (kDebugMode) {
          print('Left channel');
        }
        if (!_isDisposed) {
          remoteUsers.value = [];
        }
      },
      onError: (ErrorCodeType err, String msg) {
        if (kDebugMode) {
          print('Agora error: $err - $msg');
        }
      },
    ));

    // Configure video settings for optimal performance
    await _configureVideoSettings();

    // Enable video
    await agoraEngine!.enableVideo();

    if (isStreamer) {
      // Set client role as broadcaster
      await agoraEngine!
          .setClientRole(role: ClientRoleType.clientRoleBroadcaster);

      // Configure camera settings for better performance
      await _configureCameraSettings();

      // Enable local video preview
      await agoraEngine!.startPreview();
    } else {
      // Set client role as audience
      await agoraEngine!.setClientRole(role: ClientRoleType.clientRoleAudience);
    }
  }

  Future<void> _joinAsStreamer() async {
    if (currentStream == null) return;

    // Join channel with streamer token
    await agoraEngine!.joinChannel(
      token: currentStream!.agoraToken,
      channelId: currentStream!.channelName,
      uid: 0,
      options: const ChannelMediaOptions(
        clientRoleType: ClientRoleType.clientRoleBroadcaster,
        channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
      ),
    );
  }

  Future<void> _joinAsViewer() async {
    if (currentStream == null) return;

    try {
      // Get viewer token from API
      final result = await _apiService.joinLiveStream(currentStream!.id);
      final viewerToken = result['agoraToken'] as String;

      // Join channel with viewer token
      await agoraEngine!.joinChannel(
        token: viewerToken,
        channelId: currentStream!.channelName,
        uid: 0,
        options: const ChannelMediaOptions(
          clientRoleType: ClientRoleType.clientRoleAudience,
          channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
        ),
      );

      // Update stream info
      currentStream = result['stream'] as LiveStreamModel;
      if (!_isDisposed) {
        viewerCount.value = currentStream!.viewerCount;
        // Initialize likes data
        updateLikesFromStream();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error joining as viewer: $e');
      }
      rethrow;
    }
  }

  Future<void> _configureVideoSettings() async {
    if (agoraEngine == null) return;

    try {
      // Start with lower quality for faster initialization
      await agoraEngine!.setVideoEncoderConfiguration(
        const VideoEncoderConfiguration(
          dimensions: VideoDimensions(
              width: 480, height: 640), // 480p portrait for faster start
          frameRate: 24, // Lower FPS for faster initialization
          bitrate: 800, // Lower bitrate for faster connection
        ),
      );

      // Enable hardware acceleration for better performance
      await agoraEngine!.enableLocalVideo(true);

      // Schedule quality upgrade after connection is stable
      if (isStreamer) {
        _scheduleQualityUpgrade();
      }

      if (kDebugMode) {
        print('Video settings configured successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error configuring video settings: $e');
      }
    }
  }

  void _scheduleQualityUpgrade() {
    // Cancel any existing quality upgrade timer
    _qualityUpgradeTimer?.cancel();

    // Upgrade to higher quality after 3 seconds of stable connection
    _qualityUpgradeTimer = Timer(const Duration(seconds: 3), () async {
      if (agoraEngine != null && !_isDisposed) {
        try {
          await agoraEngine!.setVideoEncoderConfiguration(
            const VideoEncoderConfiguration(
              dimensions:
                  VideoDimensions(width: 720, height: 1280), // Upgrade to 720p
              frameRate: 30, // Upgrade to 30 FPS
              bitrate: 1500, // Upgrade bitrate
            ),
          );
          if (kDebugMode) {
            print('Video quality upgraded to 720p');
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error upgrading video quality: $e');
          }
        }
      }
    });
  }

  Future<void> _configureCameraSettings() async {
    if (agoraEngine == null) return;

    try {
      // Enable camera auto-focus for better video quality
      await agoraEngine!.setCameraAutoFocusFaceModeEnabled(true);

      // Enable camera exposure position with correct parameter names
      await agoraEngine!.setCameraExposurePosition(
        positionXinView: 0.5,
        positionYinView: 0.5,
      );

      // Set camera zoom ratio to default
      await agoraEngine!.setCameraZoomFactor(1.0);

      if (kDebugMode) {
        print('Camera settings configured successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error configuring camera settings: $e');
      }
    }
  }

  Future<void> _preWarmCamera() async {
    try {
      // Pre-initialize camera to reduce loading time
      // This helps warm up the camera hardware before Agora initialization
      if (kDebugMode) {
        print('Pre-warming camera for faster initialization');
      }

      // Small delay to allow camera hardware to initialize
      await Future.delayed(const Duration(milliseconds: 100));

      if (kDebugMode) {
        print('Camera pre-warming completed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error pre-warming camera: $e');
      }
    }
  }

  void _listenToSocketEvents() {
    // Listen for socket connection status
    _socketSubscription =
        VChatController.I.nativeStreams.socketStatusStream.listen((event) {
      if (event.isConnected) {
        // Socket reconnected, refresh viewer count
        if (currentStream != null && !_isDisposed) {
          _updateViewerCount();
        }
      }
    });

    // Listen for live stream specific events
    final socket = VChatController.I.nativeApi.remote.socketIo.socket;

    // Join the stream room to receive stream-specific events
    if (currentStream != null) {
      socket.emit('join_stream_room', {'streamId': currentStream!.id});
    }

    // Listen for stream ended event
    socket.on('live_stream_ended', (data) {
      if (_isDisposed) return;

      final streamId = data['streamId'] as String?;
      if (streamId == currentStream?.id) {
        if (kDebugMode) {
          print('Stream ended: $streamId');
        }

        // Mark stream as ended
        if (!_isDisposed) {
          streamEnded.value = true;
        }

        // Notify the view that stream ended
        onStreamEndedCallback?.call();
      }
    });

    // Listen for viewer count updates
    socket.on('stream_viewer_count_updated', (data) {
      if (_isDisposed) return;

      final streamId = data['streamId'] as String?;
      final newCount = data['viewerCount'] as int?;

      if (streamId == currentStream?.id && newCount != null && !_isDisposed) {
        viewerCount.value = newCount;
      }
    });

    // Listen for join request events (for streamers)
    socket.on('join_request_received', (data) {
      if (_isDisposed || !isStreamer) return;

      final streamId = data['streamId'] as String?;
      if (streamId == currentStream?.id) {
        // Reload join requests to get the latest list
        loadJoinRequests();
      }
    });

    // Listen for participant removal events
    socket.on('participant_removed', (data) {
      if (_isDisposed) return;

      final streamId = data['streamId'] as String?;
      final removedUserId = data['userId'] as String?;
      final reason = data['reason'] as String?;

      if (streamId == currentStream?.id && removedUserId == currentUserId) {
        if (kDebugMode) {
          print('You have been removed from the stream. Reason: $reason');
        }

        // Force leave the stream
        _handleForcedRemoval(reason);
      }
    });

    // Listen for participant ban events
    socket.on('participant_banned', (data) {
      if (_isDisposed) return;

      final streamId = data['streamId'] as String?;
      final bannedUserId = data['userId'] as String?;
      final reason = data['reason'] as String?;

      if (streamId == currentStream?.id && bannedUserId == currentUserId) {
        if (kDebugMode) {
          print('You have been banned from the stream. Reason: $reason');
        }

        // Force leave the stream
        _handleForcedRemoval(reason, isBanned: true);
      }
    });

    // Listen for direct removal events (sent specifically to the user)
    socket.on('removed_from_stream', (data) {
      if (_isDisposed) return;

      final streamId = data['streamId'] as String?;
      final reason = data['reason'] as String?;

      if (streamId == currentStream?.id) {
        if (kDebugMode) {
          print('Direct removal notification: $reason');
        }

        // Force leave the stream
        _handleForcedRemoval(reason);
      }
    });

    // Listen for direct ban events (sent specifically to the user)
    socket.on('banned_from_stream', (data) {
      if (_isDisposed) return;

      final streamId = data['streamId'] as String?;
      final reason = data['reason'] as String?;

      if (streamId == currentStream?.id) {
        if (kDebugMode) {
          print('Direct ban notification: $reason');
        }

        // Force leave the stream
        _handleForcedRemoval(reason, isBanned: true);
      }
    });

    // Listen for stream like events
    socket.on('stream_liked', (data) {
      if (_isDisposed) return;

      final streamId = data['streamId'] as String?;
      final newLikesCount = data['likesCount'] as int?;

      if (streamId == currentStream?.id &&
          newLikesCount != null &&
          !_isDisposed) {
        likesCount.value = newLikesCount;
      }
    });

    // Listen for stream unlike events
    socket.on('stream_unliked', (data) {
      if (_isDisposed) return;

      final streamId = data['streamId'] as String?;
      final newLikesCount = data['likesCount'] as int?;

      if (streamId == currentStream?.id &&
          newLikesCount != null &&
          !_isDisposed) {
        likesCount.value = newLikesCount;
      }
    });

    // Listen for filter updates from host (for participants)
    socket.on('stream_filter_updated', (data) {
      if (_isDisposed) return;

      final streamId = data['streamId'] as String?;
      final filterData = data['filterData'] as Map<String, dynamic>?;

      if (streamId == currentStream?.id && filterData != null && !isStreamer) {
        try {
          // Parse filter data and update participant's filter controller
          final filterType = FilterType.values.firstWhere(
            (e) => e.name == filterData['filterType'],
            orElse: () => FilterType.none,
          );
          final faceFilterType = FaceFilterType.values.firstWhere(
            (e) => e.name == filterData['faceFilterType'],
            orElse: () => FaceFilterType.none,
          );
          final intensity =
              (filterData['intensity'] as num?)?.toDouble() ?? 1.0;
          final isEnabled = filterData['isEnabled'] as bool? ?? false;

          final updatedFilter = StreamFilterModel(
            filterType: filterType,
            faceFilterType: faceFilterType,
            intensity: intensity,
            isEnabled: isEnabled,
          );

          // Update filter controller for participants
          _filterController.updateFilterFromHost(updatedFilter);

          if (kDebugMode) {
            print(
                'Received filter update from host: ${filterType.name}, ${faceFilterType.name}');
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error processing filter update: $e');
          }
        }
      }
    });

    // Start periodic viewer count updates as fallback
    _viewerCountTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (_isDisposed) {
        timer.cancel();
        return;
      }
      if (currentStream != null && !streamEnded.value) {
        _updateViewerCount();
      }
    });
  }

  void _setupFilterBroadcasting() {
    // Set up callback for filter changes to broadcast to participants
    _filterController.setOnFilterChangedCallback((filter) async {
      if (currentStream == null || _isDisposed) return;

      try {
        // Send filter update to backend which will broadcast to participants
        await _apiService.updateStreamFilter(
          currentStream!.id,
          filter.filterType.name,
          filter.faceFilterType.name,
          filter.intensity,
          filter.isEnabled,
        );

        if (kDebugMode) {
          print(
              'Broadcasted filter change: ${filter.filterType.name}, ${filter.faceFilterType.name}');
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error broadcasting filter change: $e');
        }
      }
    });
  }

  Future<void> _updateViewerCount() async {
    if (_isDisposed) return;

    try {
      final updatedStream = await _apiService.getStreamById(currentStream!.id);
      if (!_isDisposed) {
        viewerCount.value = updatedStream.viewerCount;
        currentStream = updatedStream;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating viewer count: $e');
      }
    }
  }

  Future<void> toggleMute() async {
    if (agoraEngine == null || _isDisposed) return;

    final newMutedState = !isMuted.value;
    await agoraEngine!.muteLocalAudioStream(newMutedState);
    if (!_isDisposed) {
      isMuted.value = newMutedState;
    }
  }

  Future<void> toggleCamera() async {
    if (agoraEngine == null || !isStreamer || _isDisposed) return;

    final newCameraState = !isCameraOn.value;
    await agoraEngine!.muteLocalVideoStream(!newCameraState);
    if (!_isDisposed) {
      isCameraOn.value = newCameraState;
    }
  }

  Future<void> switchCamera() async {
    if (agoraEngine == null || !isStreamer) return;

    await agoraEngine!.switchCamera();
  }

  Future<void> toggleSpeaker() async {
    if (agoraEngine == null || _isDisposed) return;

    final newSpeakerState = !isSpeakerOn.value;
    await agoraEngine!.setEnableSpeakerphone(newSpeakerState);
    if (!_isDisposed) {
      isSpeakerOn.value = newSpeakerState;
    }
  }

  Future<void> endStream() async {
    if (currentStream == null) return;

    try {
      if (isStreamer) {
        // End the stream on the server
        await _apiService.endLiveStream(currentStream!.id);

        if (kDebugMode) {
          print('Stream ended successfully: ${currentStream!.id}');
        }

        // Refresh the watch streams list to remove this ended stream
        _refreshWatchStreams();
      } else {
        // Leave the stream as viewer
        await _apiService.leaveLiveStream(currentStream!.id);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error ending stream: $e');
      }
    }

    // Leave Agora channel
    await _leaveChannel();
  }

  void _refreshWatchStreams() {
    try {
      // Get the WatchLiveController and refresh the streams immediately
      final watchController = GetIt.I.get<WatchLiveController>();

      // Refresh immediately
      watchController.refreshStreams();

      // Also refresh after a short delay to ensure backend has updated
      Future.delayed(const Duration(seconds: 2), () {
        watchController.refreshStreams();
      });

      if (kDebugMode) {
        print('Triggered immediate refresh of watch streams list');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error refreshing watch streams: $e');
      }
    }
  }

  Future<void> _leaveChannel() async {
    if (agoraEngine == null) return;

    await agoraEngine!.leaveChannel();
    if (isStreamer) {
      await agoraEngine!.stopPreview();
    }
  }

  void resetController() {
    // Reset state without disposing ValueNotifiers
    if (!_isDisposed) {
      // Clear callbacks first
      onStreamEndedCallback = null;
      onStreamEndedWithReasonCallback = null;

      _socketSubscription?.cancel();
      _viewerCountTimer?.cancel();
      _leaveSocketRoom();
      _leaveChannel();
      agoraEngine?.release();

      // Reset values to defaults
      isLoading.value = false;
      isMuted.value = false;
      isCameraOn.value = true;
      isSpeakerOn.value = false;
      remoteUsers.value = [];
      viewerCount.value = 0;
      streamEnded.value = false;
      likesCount.value = 0;
      isLiked.value = false;

      // Reset other properties
      agoraEngine = null;
      currentStream = null;
      isStreamer = false;
    }
  }

  void _leaveSocketRoom() {
    if (currentStream != null) {
      try {
        final socket = VChatController.I.nativeApi.remote.socketIo.socket;
        socket.emit('leave_stream_room', {'streamId': currentStream!.id});
      } catch (e) {
        if (kDebugMode) {
          print('Error leaving socket room: $e');
        }
      }
    }
  }

  void _handleForcedRemoval(String? reason, {bool isBanned = false}) {
    if (_isDisposed) return;

    // Leave the Agora channel immediately
    _leaveChannel();

    // Leave the socket room
    _leaveSocketRoom();

    // Mark stream as ended for this user
    if (!_isDisposed) {
      streamEnded.value = true;
    }

    // Use a delayed callback to ensure UI operations happen after current frame
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_isDisposed) return;

      // Notify the UI with removal information
      if (onStreamEndedWithReasonCallback != null) {
        onStreamEndedWithReasonCallback!(reason, isBanned);
      } else {
        // Fallback to the regular callback
        onStreamEndedCallback?.call();
      }
    });

    if (kDebugMode) {
      final action = isBanned ? 'banned from' : 'removed from';
      print('User was $action stream. Reason: $reason');
    }
  }

  // Participant Management Methods
  Future<List<participant_model.LiveStreamParticipantModel>> getParticipants(
      String streamId) async {
    try {
      final participants = await _apiService.getStreamParticipants(streamId);
      return participants
          .map((p) =>
              participant_model.LiveStreamParticipantModel.fromMap(p.toMap()))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting participants: $e');
      }
      throw Exception('Failed to get participants: $e');
    }
  }

  Future<void> removeParticipant({
    required String streamId,
    required String participantId,
    String? reason,
  }) async {
    try {
      await _apiService.removeParticipant(
        streamId: streamId,
        participantId: participantId,
        reason: reason,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error removing participant: $e');
      }
      throw Exception('Failed to remove participant: $e');
    }
  }

  Future<void> banParticipant({
    required String streamId,
    required String participantId,
    String? reason,
    String? duration,
  }) async {
    try {
      await _apiService.banParticipant(
        streamId: streamId,
        participantId: participantId,
        reason: reason,
        duration: duration,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error banning participant: $e');
      }
      throw Exception('Failed to ban participant: $e');
    }
  }

  Future<void> likeStream() async {
    print(
        'likeStream called - currentStream: ${currentStream?.id}, disposed: $_isDisposed'); // Debug
    if (currentStream == null || _isDisposed) {
      print('Returning early - currentStream is null or disposed'); // Debug
      return;
    }

    try {
      print(
          'Calling API service likeStream for stream: ${currentStream!.id}'); // Debug
      final result = await _apiService.likeStream(currentStream!.id);
      print('API response: $result'); // Debug

      if (!_isDisposed) {
        final newLikesCount = result['likesCount'] ?? 0;
        final newIsLiked = result['isLiked'] ?? false;
        print(
            'Updating UI - likesCount: $newLikesCount, isLiked: $newIsLiked'); // Debug

        likesCount.value = newLikesCount;
        isLiked.value = newIsLiked;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error liking stream: $e');
      }
      rethrow;
    }
  }

  Future<void> updateLikesFromStream() async {
    if (currentStream == null || _isDisposed) return;

    if (!_isDisposed) {
      likesCount.value = currentStream!.likesCount;
      isLiked.value = currentStream!.likedBy.contains(currentUserId);
    }
  }

  // Get current user ID for UI logic
  String get currentUserId {
    return AppAuth.myId;
  }

  // Join request functionality
  List<Map<String, dynamic>> _joinRequests = [];
  List<Map<String, dynamic>> get joinRequests => _joinRequests;

  Future<void> requestJoinStream() async {
    if (currentStream == null) return;

    try {
      await _apiService.requestJoinStream(currentStream!.id);
      // Show success message - we'll handle this in the UI
    } catch (e) {
      // Error will be handled in the UI
      rethrow;
    }
  }

  Future<void> loadJoinRequests() async {
    if (currentStream == null || !isStreamer) {
      if (kDebugMode) {
        print(
            "Not loading join requests - currentStream: $currentStream, isStreamer: $isStreamer");
      }
      return;
    }

    try {
      if (kDebugMode) {
        print("Loading join requests for stream: ${currentStream!.id}");
      }
      _joinRequests = await _apiService.getJoinRequests(currentStream!.id);
      if (kDebugMode) {
        print("Loaded ${_joinRequests.length} join requests: $_joinRequests");
      }
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print("Failed to load join requests: $e");
      }
    }
  }

  Future<void> respondToJoinRequest(String requestId, bool approve) async {
    try {
      final action = approve ? 'approve' : 'reject';
      await _apiService.respondToJoinRequest(requestId, action);

      // Remove the request from the list
      _joinRequests.removeWhere((request) =>
          request['id'] == requestId || request['_id'] == requestId);
      notifyListeners();

      if (kDebugMode) {
        print("Join request ${approve ? 'approved' : 'rejected'}: $requestId");
      }
    } catch (e) {
      if (kDebugMode) {
        print("Failed to respond to join request: $e");
      }
      rethrow;
    }
  }

  @override
  void dispose() {
    _isDisposed = true;

    // Clear callbacks first to prevent any further UI updates
    onStreamEndedCallback = null;
    onStreamEndedWithReasonCallback = null;

    _socketSubscription?.cancel();
    _viewerCountTimer?.cancel();
    _qualityUpgradeTimer?.cancel();
    _leaveSocketRoom();
    _leaveChannel();
    agoraEngine?.release();

    isLoading.dispose();
    loadingStatus.dispose();
    isMuted.dispose();
    isCameraOn.dispose();
    isSpeakerOn.dispose();
    remoteUsers.dispose();
    viewerCount.dispose();
    streamEnded.dispose();
    likesCount.dispose();
    isLiked.dispose();

    // Dispose filter controller
    _filterController.dispose();

    super.dispose();
  }
}
